# +============================================================================+
# | Company:   SOFiSTiK AG                                                     |
# | Version:   SOFiSTiK 2025                                                   |
# +============================================================================+

# This example has been tested with Python 3.12.2 (64-bit)

################################### IMPORT ####################################

# Import all types from "sofistik_daten.py" (automatically generated by SOFiSTiK)
from sofistik_daten import *

# Load DLL
import os

# Provides C compatible data types, and allows calling functions in DLLs
from ctypes import *

################################### SOURCE ####################################

# See for more information: https://docs.python.org/3/whatsnew/3.8.html#ctypes
os.add_dll_directory(r"C:\Program Files\SOFiSTiK\2025\SOFiSTiK 2025\interfaces\64bit")
os.add_dll_directory(r"C:\Program Files\SOFiSTiK\2025\SOFiSTiK 2025")

cdb_dll = cdll.LoadLibrary("sof_cdb_w-2025.dll")

# Connect to CDB
cdb_index = c_int()

# Set the CDB Path
filename = r"testname.cdb"

# Important: Unicode call!
cdb_index.value = cdb_dll.sof_cdb_init(filename.encode("utf8"), 99)

cdb_stat = c_int()  # get the CDB status
cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)

# Print the Status of the CDB
print("CDB Status:", cdb_stat.value)

cdb_ie = c_int(0)
rec_len = c_int(sizeof(clc_ctrl))

# 17 array elements × 4 + include 0 (17*4+1)
s = create_string_buffer(17 * 4 + 1)

"""
do while ie == 0, see cdbase.chm, Returnvalue.
    = 0 -> No error
    = 1 -> Item is longer than Data
    = 2 -> End of file reached
    = 3 -> Key does not exist
"""
while cdb_ie.value < 2:
    cdb_ie.value = cdb_dll.sof_cdb_get(
        cdb_index, 12, 1, byref(clc_ctrl), byref(rec_len), 1,
    )

    if cdb_ie.value < 2:
        cdb_dll.sof_lib_ps2cs(byref(clc_ctrl.m_rtex), byref(s), sizeof(s))
        print(s.value)

    # Always read the length of record before sof_cdb_get is called
    rec_len = c_int(sizeof(clc_ctrl))

# Close the CDB, 0 - will close all the files
cdb_dll.sof_cdb_close(0)

# Print again the status of the CDB, if status = 0 -> CDB Closed successfully
cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)
if cdb_stat.value == 0:
    print("CDB closed successfully, status = 0")

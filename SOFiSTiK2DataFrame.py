"""
SOFiSTiK CDB to DataFrame converter.

Soares, Paulo 29 September 2025
Import Nodes from SOFiSTiK CDB into Data Frames
"""

import os
from ctypes import *
from sofistik_daten import *
import pandas as pd
from tabulate import tabulate
import matplotlib.pyplot as plt


def get_nodes(path_file):
    """
    Extract nodes from SOFiSTiK CDB file and return as pandas DataFrame.

    Args:
        path_file (str): Path to the CDB file

    Returns:
        pd.DataFrame: DataFrame containing node data with columns:
                     NodeNumber, X, Y, Z
    """

    os.add_dll_directory(
        r"C:\Program Files\SOFiSTiK\2026\SOFiSTiK 2026\interfaces\64bit")
    os.add_dll_directory(r"C:\Program Files\SOFiSTiK\2026\SOFiSTiK 2026")
    cdb_dll = cdll.LoadLibrary("sof_cdb_w-2026.dll")

    # Connect to CDB
    cdb_index = c_int()

    # Important: Unicode call!
    cdb_index.value = cdb_dll.sof_cdb_init(path_file.encode("utf-8"), 99)

    # Get the CDB status
    cdb_stat = c_int()
    cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)

    # Print the Status of the CDB
    print("CDB Status:", cdb_stat.value)

    cdb_ie = c_int(0)
    rec_len = c_int(sizeof(cnode))

    nodenum = []
    intnodenum = []
    dof = []
    aditbitcode = []
    xcoord = []
    ycoord = []
    zcoord = []

    while cdb_ie.value < 2:
        cdb_ie.value = cdb_dll.sof_cdb_get(
            cdb_index, 20, 0, byref(cnode), byref(rec_len), 1)
        nodenum.append(cnode.m_nr)          # node-number
        xcoord.append(cnode.m_xyz[0])       # x coordinates
        ycoord.append(cnode.m_xyz[1])       # y coordinates
        zcoord.append(cnode.m_xyz[2])       # z coordinates
        intnodenum.append(cnode.m_inr)      # internal node-number
        dof.append(cnode.m_kfix)            # degree of freedoms
        aditbitcode.append(cnode.m_ncod)    # additional bit code

    # Store Data into a dictionary
    data = {'Node Number': nodenum, 'X Coordinate': xcoord, 'Y Coordinate': ycoord, 'Z Coordinate': zcoord}

    print("Data Frame created successfully")

    # Always read the length of record before sof_cdb_get is called
    rec_len = c_int(sizeof(cnode))

    # Close the CDB, 0 - will close all the files
    cdb_dll.sof_cdb_close(0)

    # Print again the status of the CDB, if status = 0 -> CDB Closed successfully
    cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)
    if cdb_stat.value == 0:
        print("CDB closed successfully, status = 0")

    return pd.DataFrame(data)

def plot_nodes(df):
    """
    Plot nodes from DataFrame.

    Args:
        df (pd.DataFrame): DataFrame containing node data with columns:
                           NodeNumber, X, Y, Z
    """
    
    plt.scatter(df['X Coordinate'], df['Y Coordinate'])
    plt.show()

if __name__ == "__main__":
    # Input the cdb path here
    CDB_PATH = r"SteelFrame.cdb"
    result_df = get_nodes(CDB_PATH)
    print("\n📌 \033[1;32mPoints Coordinates:\033[0m ")
    print(tabulate(result_df, headers='keys', tablefmt='fancy_grid', showindex=False))

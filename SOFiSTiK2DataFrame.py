"""
SOFiSTiK CDB to DataFrame converter.

Soares, Paulo 29 September 2025
Import Nodes from SOFiSTiK CDB into Data Frames
"""

import os
from ctypes import c_int, cdll, byref, sizeof
import pandas as pd
from sofistik_daten import cnode


def get_nodes(path_file):
    """
    Extract nodes from SOFiSTiK CDB file and return as pandas DataFrame.

    Args:
        path_file (str): Path to the CDB file

    Returns:
        pd.DataFrame: DataFrame containing node data with columns:
                     NodeNumber, X, Y, Z
    """

    os.add_dll_directory(
        r"C:\Program Files\SOFiSTiK\2026\SOFiSTiK 2026\interfaces\64bit")
    os.add_dll_directory(r"C:\Program Files\SOFiSTiK\2026\SOFiSTiK 2026")
    cdb_dll = cdll.LoadLibrary("sof_cdb_w-2026.dll")

    # Connect to CDB
    cdb_index = c_int()

    # Important: Unicode call!
    cdb_index.value = cdb_dll.sof_cdb_init(path_file.encode("utf-8"), 99)

    # Get the CDB status
    cdb_stat = c_int()
    cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)

    # Print the Status of the CDB
    print("CDB Status:", cdb_stat.value)

    # Check if CDB was opened successfully
    if cdb_stat.value != 1:
        print(f"Error: Could not open CDB file '{path_file}'. Status: {cdb_stat.value}")
        return pd.DataFrame()  # Return empty DataFrame on error

    cdb_ie = c_int(0)
    rec_len = c_int(sizeof(cnode))

    node_numbers = []
    internal_node_numbers = []
    degrees_of_freedom = []
    additional_bit_codes = []
    x_coordinates = []
    y_coordinates = []
    z_coordinates = []

    while cdb_ie.value < 2:
        cdb_ie.value = cdb_dll.sof_cdb_get(
            cdb_index, 20, 0, byref(cnode), byref(rec_len), 1)

        # Only append data if we successfully read a record
        if cdb_ie.value == 0:  # 0 indicates successful read
            node_numbers.append(cnode.m_nr)      # node-number
            x_coordinates.append(cnode.m_xyz[0])        # x coordinates
            y_coordinates.append(cnode.m_xyz[1])        # y coordinates
            z_coordinates.append(cnode.m_xyz[2])        # z coordinates
            internal_node_numbers.append(cnode.m_inr)   # internal node-number
            degrees_of_freedom.append(cnode.m_kfix)        # degree of freedoms
            additional_bit_codes.append(cnode.m_ncod)  # additional bit code

    # Store Data into a dictionary
    data = {
        'NodeNumber': node_numbers,
        'X': x_coordinates,
        'Y': y_coordinates,
        'Z': z_coordinates
    }

    print("Data Frame created successfully")

    # Close the CDB, 0 - will close all the files
    cdb_dll.sof_cdb_close(0)

    # Print again the status of the CDB, if status = 0 -> CDB Closed successfully
    cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)
    if cdb_stat.value == 0:
        print("CDB closed successfully, status = 0")

    return pd.DataFrame(data)


if __name__ == "__main__":
    # Input the cdb path here
    CDB_PATH = r"SteelFrame.cdb"
    result_df = get_nodes(CDB_PATH)
    print(result_df)

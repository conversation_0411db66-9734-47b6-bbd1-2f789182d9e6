# Soares, Paulo 29 September 2025
# Import Nodes from SOFiSTiK CDB into Data Frames

import os
from ctypes import *
from sofistik_daten import *
import pandas as pd


def getnodes(Pathfile):

    os.add_dll_directory(
        r"C:\Program Files\SOFiSTiK\2026\SOFiSTiK 2026\interfaces\64bit")
    os.add_dll_directory(r"C:\Program Files\SOFiSTiK\2026\SOFiSTiK 2026")
    cdb_dll = cdll.LoadLibrary("sof_cdb_w-2026.dll")

    # Connect to CDB
    cdb_index = c_int()

    # Important: Unicode call!
    cdb_index.value = cdb_dll.sof_cdb_init(Pathfile.encode("utf-8"), 99)

    # Get the CDB status
    cdb_stat = c_int()
    cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)

    # Print the Status of the CDB
    print("CDB Status:", cdb_stat.value)

    cdb_ie = c_int(0)
    rec_len = c_int(sizeof(cnode))

    nodenum = []
    intnodenum = []
    dof = []
    aditbitcode = []
    xcoord = []
    ycoord = []
    zcoord = []

    while cdb_ie.value < 2:
        cdb_ie.value = cdb_dll.sof_cdb_get(
            cdb_index, 20, 0, byref(cnode), byref(rec_len), 1)
        nodenum.append(cnode.m_nr)      # node-number
        xcoord.append(cnode.m_xyz[0])        # x coordinates
        ycoord.append(cnode.m_xyz[1])        # y coordinates
        zcoord.append(cnode.m_xyz[2])        # z coordinates
        intnodenum.append(cnode.m_inr)   # internal node-number
        dof.append(cnode.m_kfix)        # degree of freedoms
        aditbitcode.append(cnode.m_ncod)  # additional bit code

    # Store Data into a dictionary
    data = {'NodeNumber': nodenum, 'X': xcoord, 'Y': ycoord, 'Z': zcoord}

    print("Data Frame created successfully")

    # Always read the length of record before sof_cdb_get is called
    rec_len = c_int(sizeof(cnode))

    # Close the CDB, 0 - will close all the files
    cdb_dll.sof_cdb_close(0)

    # Print again the status of the CDB, if status = 0 -> CDB Closed successfully
    cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)
    if cdb_stat.value == 0:
        print("CDB closed successfully, status = 0")

    return pd.DataFrame(data)


# Input the cdb path here
pathfile = r"SteelFrame.cdb"
getnodes(pathfile)

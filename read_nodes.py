# +============================================================================+
# | Company:   SOFiSTiK AG                                                     |
# | Version:   SOFiSTiK 2025                                                   |
# +============================================================================+

# This example has been tested with Python 3.12.2 (64-bit)

################################### IMPORT ####################################

# Import all types from "sofistik_daten.py" (automatically generated by SOFiSTiK)
from sofistik_daten import *

# Load DLL
import os

# Provides C compatible data types, and allows calling functions in DLLs
from ctypes import *

################################### SOURCE ####################################

# This example has been tested with Python 3.12.2 (64-bit)

# See for more information: https://docs.python.org/3/whatsnew/3.8.html#ctypes
os.add_dll_directory(r"C:\Program Files\SOFiSTiK\2026\SOFiSTiK 2026\interfaces\64bit")
os.add_dll_directory(r"C:\Program Files\SOFiSTiK\2026\SOFiSTiK 2026")

cdb_dll = cdll.LoadLibrary("sof_cdb_w-2026.dll")

# Connect to CDB
cdb_index = c_int()

# Input the cdb path here
filename = r"SteelFrame.cdb"

# Important: Unicode call!
cdb_index.value = cdb_dll.sof_cdb_init(filename.encode("utf-8"), 99)

# Get the CDB status
cdb_stat = c_int()
cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)

# Print the Status of the CDB
print("CDB Status:", cdb_stat.value)

cdb_ie = c_int(0)
rec_len = c_int(sizeof(cnode))

"""
do while ie == 0, see cdbase.chm, Returnvalue.
    = 0 -> No error
    = 1 -> Item is longer than Data
    = 2 -> End of file reached
    = 3 -> Key does not exist
"""
while cdb_ie.value < 2:
    cdb_ie.value = cdb_dll.sof_cdb_get(
        cdb_index, 20, 0, byref(cnode), byref(rec_len), 1
    )

    print(
        "{:10d}{:10d}{:10d}{:10d}{:10.2f}{:10.2f}{:10.2f}".format(
            cnode.m_nr,  # node-number
            cnode.m_inr,  # internal node-number
            cnode.m_kfix,  # degree of freedoms
            cnode.m_ncod,  # additional bit code
            cnode.m_xyz[0],  # x coordinates
            cnode.m_xyz[1],  # y coordinates
            cnode.m_xyz[2],  # z coordinates
        )
    )

    # Always read the length of record before sof_cdb_get is called
    rec_len = c_int(sizeof(cnode))

# Close the CDB, 0 - will close all the files
cdb_dll.sof_cdb_close(0)

# Print again the status of the CDB, if status = 0 -> CDB Closed successfully
cdb_stat.value = cdb_dll.sof_cdb_status(cdb_index.value)
if cdb_stat.value == 0:
    print("CDB closed successfully, status = 0")
